import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Co<PERSON>, Sparkles, Check, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const Index = () => {
  const { toast } = useToast();
  const [password, setPassword] = useState('');
  const [charCount, setCharCount] = useState([12]);
  const [includeNumbers, setIncludeNumbers] = useState(true);
  const [includeSymbols, setIncludeSymbols] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showParticles, setShowParticles] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);

  // Characters for different types
  const LOWERCASE = 'abcdefghijklmnopqrstuvwxyz';
  const UPPERCASE = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const NUMBERS = '0123456789';
  const SYMBOLS = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  // Generate random password with character count
  const generateRandomPassword = (length: number) => {
    let chars = LOWERCASE + UPPERCASE;
    if (includeNumbers) chars += NUMBERS;
    if (includeSymbols) chars += SYMBOLS;
    
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // Auto copy to clipboard
  const autoCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.log('Auto copy failed');
    }
  };

  // Manual copy to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
      
      toast({
        title: "✅ Copied!",
        description: "Password copied to clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy the password manually.",
        variant: "destructive",
      });
    }
  };

  // Calculate password strength
  const calculateStrength = (pwd: string) => {
    let score = 0;
    score += Math.min(pwd.length * 4, 40);
    if (/[a-z]/.test(pwd)) score += 10;
    if (/[A-Z]/.test(pwd)) score += 10;
    if (/[0-9]/.test(pwd)) score += 15;
    if (/[^A-Za-z0-9]/.test(pwd)) score += 15;
    return Math.min(score, 100);
  };

  // Password generation with animation
  const generateInstantPassword = () => {
    setIsGenerating(true);
    setShowParticles(true);

    // Haptic feedback on mobile
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }

    setTimeout(() => {
      const newPassword = generateRandomPassword(charCount[0]);
      setPassword(newPassword);
      setIsGenerating(false);
      setHasGenerated(true);
      
      // Auto copy password
      autoCopyToClipboard(newPassword);
      
      // Hide particles after animation
      setTimeout(() => setShowParticles(false), 1000);
    }, 150);
  };

  // Particle Effect Component
  const ParticleEffect = () => (
    <div className="particle-container absolute inset-0 pointer-events-none">
      {[...Array(5)].map((_, i) => (
        <div 
          key={i} 
          className="particle absolute top-1/2 left-1/2"
          style={{ 
            '--delay': `${i * 0.1}s`,
            left: `${50 + (i - 2) * 10}%`,
            top: `${50 + (i % 2) * 10}%`
          } as React.CSSProperties}
        >
          ✨
        </div>
      ))}
    </div>
  );

  // Strength Meter Component
  const StrengthMeter = React.memo(({ password }: { password: string }) => {
    const strength = calculateStrength(password);
    const emoji = ['😟', '😐', '🙂', '😊', '🤩'][Math.floor(strength / 20)] || '🤩';
    const messages = [
      "Needs more complexity!",
      "Getting there...",
      "Pretty good!",
      "Super secure!",
      "Fort Knox level!"
    ];
    const message = messages[Math.floor(strength / 20)] || messages[4];
    
    return (
      <div className="text-center">
        <div className="text-4xl mb-2 strength-pulse">{emoji}</div>
        <Progress value={strength} className="h-3 mb-2" />
        <p className="text-sm font-medium text-muted-foreground">{message}</p>
      </div>
    );
  });

  return (
    <div className={`min-h-screen ${hasGenerated ? 'bg-success-gradient' : 'bg-purple-gradient'}`}>
      {/* Minimalistic Header */}
      <div className="text-center py-8">
        <h1 className="text-4xl font-bold">
          <span className="text-primary">Silly</span>
          <span className="text-accent">Pass</span> 
          <span className="text-2xl ml-2">🎉</span>
        </h1>
      </div>

      {/* Main Content */}
      <div className="max-w-md mx-auto px-4 pb-8">
        {/* Main Generator Card */}
        <Card className="bg-white/20 backdrop-blur-lg border-0 shadow-2xl p-8 relative overflow-hidden">
          {showParticles && <ParticleEffect />}
          
          <CardContent className="space-y-6 p-0">
            {/* Password Display with Copy Icon */}
            {password && (
              <div className={`password-reveal ${isGenerating ? 'password-generating' : ''}`}>
                <div className="bg-white/90 rounded-2xl p-6 text-center shadow-lg relative">
                  <div className="flex items-center justify-center gap-3">
                    <p className="text-xl font-bold text-gray-800 break-all flex-1">
                      {password}
                    </p>
                    <button
                      onClick={() => copyToClipboard(password)}
                      className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      {copySuccess ? (
                        <Check className="h-5 w-5 text-success" />
                      ) : (
                        <Copy className="h-5 w-5 text-gray-600" />
                      )}
                    </button>
                  </div>
                  {copySuccess && (
                    <div className="absolute -top-2 -right-2 bg-success text-success-foreground px-2 py-1 rounded-full text-xs font-medium">
                      Copied
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Big Generate Button */}
            <Button
              onClick={generateInstantPassword}
              disabled={isGenerating}
              className="w-full h-16 text-lg btn-bounce bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="animate-spin mr-2" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2" />
                  Generate Password
                </>
              )}
            </Button>

            {/* Options */}
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-semibold">Character Count</Label>
                  <span className="text-sm font-medium">{charCount[0]}</span>
                </div>
                <Slider
                  value={charCount}
                  onValueChange={setCharCount}
                  min={5}
                  max={20}
                  step={1}
                  className="w-full"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">Include Numbers</Label>
                <Switch
                  checked={includeNumbers}
                  onCheckedChange={setIncludeNumbers}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">Include Symbols</Label>
                <Switch
                  checked={includeSymbols}
                  onCheckedChange={setIncludeSymbols}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Strength Indicator */}
        {password && (
          <Card className="mt-4 p-4 bg-white/20 backdrop-blur-lg border-0 shadow-lg">
            <StrengthMeter password={password} />
          </Card>
        )}
      </div>
    </div>
  );
};

export default Index;
