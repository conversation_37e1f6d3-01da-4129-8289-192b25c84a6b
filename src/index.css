@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 280 30% 95%;
    --foreground: 280 15% 9%;

    --card: 0 0% 100%;
    --card-foreground: 280 15% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 280 15% 9%;

    --primary: 280 100% 70%;
    --primary-foreground: 0 0% 100%;

    --secondary: 280 30% 95%;
    --secondary-foreground: 280 15% 9%;

    --accent: 9 83% 77%;
    --accent-foreground: 280 15% 9%;

    --yellow: 54 91% 77%;
    --yellow-foreground: 280 15% 9%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Bouncy button press */
  .btn-bounce {
    @apply transition-all duration-150 active:scale-95 hover:scale-105;
    animation: microBounce 0.3s ease-out;
  }
  
  /* Password reveal animation */
  .password-reveal {
    animation: slideInBounce 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  /* Strength meter pulse */
  .strength-pulse {
    animation: strengthPulse 1s ease-in-out infinite;
  }
  
  /* Password generation animation */
  .password-generating {
    animation: passwordPulse 0.5s ease-out;
  }
  
  /* Static background gradients */
  .bg-purple-gradient {
    background: linear-gradient(135deg, hsl(280 15% 92%), hsl(280 10% 96%));
    transition: background 1s ease-in-out;
    position: relative;
  }
  
  .bg-purple-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, transparent 0%, transparent 50%, rgba(255,255,255,0.3) 50.5%, rgba(255,255,255,0.3) 51%, transparent 51.5%),
      radial-gradient(circle at 75% 75%, transparent 0%, transparent 50%, rgba(255,255,255,0.2) 50.5%, rgba(255,255,255,0.2) 51%, transparent 51.5%);
    background-size: 20px 20px;
    opacity: 0.6;
    pointer-events: none;
  }
  
  .bg-success-gradient {
    background: linear-gradient(135deg, hsl(142 20% 88%), hsl(142 15% 94%));
    transition: background 1s ease-in-out;
    position: relative;
  }
  
  .bg-success-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, transparent 0%, transparent 50%, rgba(255,255,255,0.3) 50.5%, rgba(255,255,255,0.3) 51%, transparent 51.5%),
      radial-gradient(circle at 75% 75%, transparent 0%, transparent 50%, rgba(255,255,255,0.2) 50.5%, rgba(255,255,255,0.2) 51%, transparent 51.5%);
    background-size: 20px 20px;
    opacity: 0.6;
    pointer-events: none;
  }
  
  /* Particle effects */
  .particle-container {
    position: relative;
    overflow: hidden;
  }
  
  .particle {
    position: absolute;
    animation: particleFloat 1s ease-out;
    animation-delay: var(--delay, 0s);
    pointer-events: none;
    font-size: 1.5rem;
  }
  
  @keyframes microBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
  }
  
  @keyframes slideInBounce {
    0% { 
      opacity: 0; 
      transform: translateY(20px) scale(0.9); 
    }
    60% { 
      transform: translateY(-5px) scale(1.02); 
    }
    100% { 
      opacity: 1; 
      transform: translateY(0) scale(1); 
    }
  }
  
  @keyframes strengthPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }
  
  @keyframes passwordPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
  
  @keyframes particleFloat {
    0% { 
      opacity: 1; 
      transform: translateY(0) translateX(0) rotate(0deg); 
    }
    100% { 
      opacity: 0; 
      transform: translateY(-100px) translateX(50px) rotate(180deg); 
    }
  }
}